const _0x1c5c32 = _0x46af;
(function (_0x58190e, _0x5ba289) {
  const _0x1aae4e = _0x46af,
    _0x4dcb12 = _0x58190e();
  while (!![]) {
    try {
      const _0x5d10ca =
        (-parseInt(_0x1aae4e(0xfb)) / 0x1) *
          (-parseInt(_0x1aae4e(0x129)) / 0x2) +
        -parseInt(_0x1aae4e(0xfc)) / 0x3 +
        parseInt(_0x1aae4e(0x125)) / 0x4 +
        parseInt(_0x1aae4e(0x114)) / 0x5 +
        (parseInt(_0x1aae4e(0xf9)) / 0x6) *
          (-parseInt(_0x1aae4e(0x116)) / 0x7) +
        (-parseInt(_0x1aae4e(0x10c)) / 0x8) *
          (-parseInt(_0x1aae4e(0x117)) / 0x9) +
        (-parseInt(_0x1aae4e(0x101)) / 0xa) * (parseInt(_0x1aae4e(0xfe)) / 0xb);
      if (_0x5d10ca === _0x5ba289) break;
      else _0x4dcb12["push"](_0x4dcb12["shift"]());
    } catch (_0x3c99cb) {
      _0x4dcb12["push"](_0x4dcb12["shift"]());
    }
  }
})(_0x29c9, 0x63421);
async function fetchProjects() {
  const _0x535ae1 = _0x46af;
  try {
    const _0x433012 = await fetch(_0x535ae1(0x12e));
    if (!_0x433012["ok"]) throw new Error(_0x535ae1(0x113));
    const _0x2dd129 = await _0x433012[_0x535ae1(0x105)]();
    return _0x2dd129["projects"];
  } catch (_0x7dd94) {
    return console[_0x535ae1(0x121)](_0x535ae1(0x110), _0x7dd94), [];
  }
}
function createProjectCard(_0xf850b, _0x100074) {
  const _0x1309c8 = _0x46af,
    _0x4b732f = document["createElement"](_0x1309c8(0x109));
  return (
    (_0x4b732f[_0x1309c8(0x11c)] =
      "project-card\x20bg-white\x20rounded-lg\x20overflow-hidden\x20shadow-md\x20animate-fade-in\x20delay-" +
      _0x100074),
    (_0x4b732f[_0x1309c8(0x127)] =
      _0x1309c8(0x103) +
      _0xf850b["image"] +
      _0x1309c8(0x104) +
      _0xf850b[_0x1309c8(0xfd)] +
      "\x22\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20class=\x22w-full\x20h-full\x20object-cover\x20transition-transform\x20duration-500\x20hover:scale-105\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22p-6\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<h3\x20class=\x22text-xl\x20font-semibold\x20mb-2\x22>" +
      _0xf850b["title"] +
      _0x1309c8(0x124) +
      _0xf850b[_0x1309c8(0x10f)] +
      _0x1309c8(0x123) +
      _0xf850b[_0x1309c8(0x122)]
        [_0x1309c8(0xff)](
          (_0x43225f) =>
            _0x1309c8(0x118) +
            _0x43225f[_0x1309c8(0x12b)] +
            _0x1309c8(0x12c) +
            _0x43225f["color"] +
            _0x1309c8(0x111) +
            _0x43225f[_0x1309c8(0x11d)] +
            _0x1309c8(0x100)
        )
        [_0x1309c8(0x11a)]("") +
      "\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22flex\x20space-x-3\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20" +
      (_0xf850b[_0x1309c8(0x102)]
        ? _0x1309c8(0x115) + _0xf850b[_0x1309c8(0x102)] + _0x1309c8(0x120)
        : "") +
      _0x1309c8(0x11b) +
      (_0xf850b["sourceLink"]
        ? "<a\x20href=\x22" + _0xf850b[_0x1309c8(0x108)] + _0x1309c8(0x11e)
        : "") +
      "\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20"),
    _0x4b732f
  );
}
function _0x46af(_0x33d8e0, _0x483cab) {
  const _0x29c9d3 = _0x29c9();
  return (
    (_0x46af = function (_0x46af57, _0x376d56) {
      _0x46af57 = _0x46af57 - 0xf9;
      let _0x3daa6e = _0x29c9d3[_0x46af57];
      return _0x3daa6e;
    }),
    _0x46af(_0x33d8e0, _0x483cab)
  );
}
function _0x29c9() {
  const _0x54bb0e = [
    "title",
    "6743tjShKa",
    "map",
    "</span>",
    "14230vcyNzd",
    "liveLink",
    "\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22h-48\x20overflow-hidden\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<img\x20src=\x22/img/projects/",
    "\x22\x20alt=\x22",
    "json",
    "createElement",
    "getElementById",
    "sourceLink",
    "div",
    "<p\x20class=\x22text-gray-600\x22>No\x20projects\x20found.</p>",
    "Error\x20rendering\x20projects:",
    "856312BLAsvQ",
    "querySelector",
    "projects-loading",
    "description",
    "Error\x20loading\x20projects:",
    "-800\x20px-3\x20py-1\x20rounded-full\x20text-sm\x22>",
    "remove",
    "Failed\x20to\x20fetch\x20projects\x20data",
    "2211075KANsxw",
    "<a\x20href=\x22",
    "336455MohFUi",
    "54OgQCFP",
    "<span\x20class=\x22tech-tag\x20bg-",
    "addEventListener",
    "join",
    "\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20",
    "className",
    "name",
    "\x22\x20class=\x22text-gray-600\x20hover:text-indigo-600\x20transition\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<i\x20class=\x22fab\x20fa-github\x22></i>\x20Source\x20Code\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</a>",
    "Projects\x20container\x20not\x20found",
    "\x22\x20target=\x22_blank\x22\x20class=\x22text-indigo-600\x20hover:text-indigo-800\x20transition\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<i\x20class=\x22fas\x20fa-external-link-alt\x22></i>\x20Live\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</a>",
    "error",
    "technologies",
    "</p>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22flex\x20flex-wrap\x20gap-2\x20mb-4\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20",
    "</h3>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22text-gray-600\x20mb-4\x22>",
    "2386684WBigKZ",
    "forEach",
    "innerHTML",
    "DOMContentLoaded",
    "2IXJmji",
    "appendChild",
    "color",
    "-100\x20text-",
    "col-span-3\x20text-center\x20py-12",
    "/data.json",
    "66sjhjUR",
    "#projects\x20.grid",
    "727161uAEyyY",
    "1802118zIUBTh",
  ];
  _0x29c9 = function () {
    return _0x54bb0e;
  };
  return _0x29c9();
}
async function renderProjects() {
  const _0x4982d6 = _0x46af,
    _0x26e3b8 = document[_0x4982d6(0x10d)](_0x4982d6(0xfa));
  if (!_0x26e3b8) {
    console["error"](_0x4982d6(0x11f));
    return;
  }
  const _0x29ea64 = document[_0x4982d6(0x107)](_0x4982d6(0x10e));
  try {
    const _0x3ee70c = await fetchProjects();
    _0x29ea64 && _0x29ea64[_0x4982d6(0x112)]();
    if (_0x3ee70c["length"] === 0x0) {
      const _0x33b6bd = document[_0x4982d6(0x106)]("div");
      (_0x33b6bd[_0x4982d6(0x11c)] = _0x4982d6(0x12d)),
        (_0x33b6bd[_0x4982d6(0x127)] = _0x4982d6(0x10a)),
        _0x26e3b8[_0x4982d6(0x12a)](_0x33b6bd);
      return;
    }
    _0x3ee70c[_0x4982d6(0x126)]((_0x17e2c2, _0x4323aa) => {
      const _0x1bf6e7 = _0x4982d6,
        _0x7a376f = (_0x4323aa % 0x3) * 0x64 + 0x64,
        _0x21a352 = createProjectCard(_0x17e2c2, _0x7a376f);
      _0x26e3b8[_0x1bf6e7(0x12a)](_0x21a352);
    });
  } catch (_0x561b5e) {
    console["error"](_0x4982d6(0x10b), _0x561b5e);
    _0x29ea64 && _0x29ea64[_0x4982d6(0x112)]();
    const _0x14e6dd = document["createElement"](_0x4982d6(0x109));
    (_0x14e6dd["className"] = _0x4982d6(0x12d)),
      (_0x14e6dd[_0x4982d6(0x127)] =
        "<p\x20class=\x22text-red-600\x22>Failed\x20to\x20load\x20projects.\x20Please\x20try\x20again\x20later.</p>"),
      _0x26e3b8[_0x4982d6(0x12a)](_0x14e6dd);
  }
}
document[_0x1c5c32(0x119)](_0x1c5c32(0x128), renderProjects);
