<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bagus <PERSON>zar - Resume</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @media print {
            body { 
                -webkit-print-color-adjust: exact; 
                print-color-adjust: exact;
                margin: 0;
                padding: 0;
            }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
        }
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.5;
        }
        .resume-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="resume-container">
        <!-- Header Section -->
        <div class="bg-white p-8">
            <div class="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
                <div class="flex-shrink-0">
                    <div class="w-32 h-32 rounded-full overflow-hidden border-4 border-indigo-200">
                        <img src="/img/photo-bagus-almahenzar.JPG" alt="Profile Photo of Bagus Almahenzar"
                            class="w-full h-full object-cover">
                    </div>
                </div>
                <div class="md:w-2/3 text-center md:text-left">
                    <h1 class="text-3xl font-bold text-gray-900">Bagus Almahenzar</h1>
                    <h2 class="text-xl text-indigo-600 mb-2">Full Stack Web Developer | Software Engineer</h2>
                    <p class="text-gray-600">Specializing in Laravel, React, Flutter, and JavaScript development
                        with 5+ years of experience in web applications, mobile apps, and government systems (SPBE)
                    </p>
                    <div class="mt-4 text-sm text-gray-600">
                        <p><i class="fas fa-envelope mr-2"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt mr-2"></i> Pontianak, West Kalimantan, Indonesia</p>
                        <p><i class="fab fa-github mr-2"></i> github.com/Dipterocarpaceae</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Experience Section -->
        <div class="bg-white px-8 py-6 border-t border-gray-200">
            <h3 class="text-xl font-semibold mb-6 text-indigo-600">Experience</h3>

            <div class="space-y-6">
                <div>
                    <h4 class="font-bold text-gray-800">Pranata Komputer Ahli Pertama</h4>
                    <p class="text-gray-600">BPS-Statistics Pontianak Municipality • 2023 - Present</p>
                    <ul class="list-disc pl-5 mt-2 text-gray-600 space-y-1">
                        <li>Led development of Laravel-based CRM system with Filament admin panel, improving
                            client management efficiency by 40%</li>
                        <li>Built responsive React frontend for customer portal with Express.js backend API,
                            serving 500+ active users</li>
                        <li>Responsible for electronic-based government system (SPBE) implementation and
                            maintenance</li>
                        <li>Developed automated reporting systems reducing manual data processing time by 60%
                        </li>
                    </ul>

                    <div class="mt-4">
                        <h5 class="font-semibold text-gray-700 mb-2">Key Projects:</h5>
                        <div class="space-y-3">
                            <div class="bg-gray-50 p-3 rounded">
                                <h6 class="font-medium text-gray-800">Statistics Indonesia Datalab (StatInaLab)</h6>
                                <p class="text-sm text-gray-600">Initial system developer for secure microdata
                                    access platform using 5 Safes framework</p>
                                <p class="text-xs text-indigo-600 mt-1">Laravel • Flutter • Tailwind CSS • MySQL</p>
                            </div>

                            <div class="bg-gray-50 p-3 rounded">
                                <h6 class="font-medium text-gray-800">SEPAKAT - Meeting Management System</h6>
                                <p class="text-sm text-gray-600">CRM system for BPS Statistics with lead
                                    tracking and reporting features</p>
                                <p class="text-xs text-indigo-600 mt-1">Laravel • Filament • MySQL</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-6">
                    <h4 class="font-bold text-gray-800">Web Developer</h4>
                    <p class="text-gray-600">Digital Creations • 2018 - 2020</p>
                    <ul class="list-disc pl-5 mt-2 text-gray-600 space-y-1">
                        <li>Developed and maintained 15+ Laravel applications for various clients across
                            different industries</li>
                        <li>Created custom WordPress themes and plugins, increasing client website performance
                            by 35%</li>
                        <li>Implemented responsive designs using Tailwind CSS and Bootstrap frameworks</li>
                        <li>Optimized database queries and application performance, reducing page load times by
                            50%</li>
                    </ul>

                    <div class="mt-4">
                        <h5 class="font-semibold text-gray-700 mb-2">Key Projects:</h5>
                        <div class="space-y-3">
                            <div class="bg-gray-50 p-3 rounded">
                                <h6 class="font-medium text-gray-800">Ngambiskuy Educational Platform</h6>
                                <p class="text-sm text-gray-600">Comprehensive educational platform with
                                    interactive exam simulations and learning management system</p>
                                <p class="text-xs text-indigo-600 mt-1">Laravel • Filament • JavaScript •
                                    Tailwind CSS</p>
                            </div>

                            <div class="bg-gray-50 p-3 rounded">
                                <h6 class="font-medium text-gray-800">Indonesian Physiotherapy Association
                                    Mobile App</h6>
                                <p class="text-sm text-gray-600">Cross-platform mobile application with offline
                                    data synchronization and member management</p>
                                <p class="text-xs text-indigo-600 mt-1">Flutter • Dart • SQLite • REST API</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Education Section -->
        <div class="bg-white px-8 py-6 border-t border-gray-200">
            <h3 class="text-xl font-semibold mb-6 text-indigo-600">Education</h3>

            <div class="space-y-4">
                <div>
                    <h4 class="font-bold text-gray-800">B.Sc. in Statistical Computing</h4>
                    <p class="text-gray-600">Polytechnic of Statistics STIS • 2019 - 2023</p>
                </div>
                <div>
                    <h4 class="font-bold text-gray-800">Senior High School</h4>
                    <p class="text-gray-600">SMAN 02 Singkawang • 2016 - 2019</p>
                </div>
            </div>
        </div>

        <!-- Skills Section -->
        <div class="bg-white px-8 py-6 border-t border-gray-200">
            <h3 class="text-xl font-semibold mb-6 text-indigo-600">Technical Skills</h3>

            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fab fa-laravel text-indigo-600 text-xs"></i>
                    </div>
                    <span>Laravel</span>
                </div>
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fab fa-react text-blue-600 text-xs"></i>
                    </div>
                    <span>React</span>
                </div>
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fab fa-node-js text-green-600 text-xs"></i>
                    </div>
                    <span>Express.js</span>
                </div>
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-teal-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fas fa-mobile-alt text-teal-600 text-xs"></i>
                    </div>
                    <span>Flutter</span>
                </div>
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fab fa-js text-yellow-600 text-xs"></i>
                    </div>
                    <span>JavaScript</span>
                </div>
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fab fa-css3-alt text-blue-600 text-xs"></i>
                    </div>
                    <span>Tailwind CSS</span>
                </div>
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fab fa-php text-orange-600 text-xs"></i>
                    </div>
                    <span>PHP</span>
                </div>
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fas fa-database text-green-600 text-xs"></i>
                    </div>
                    <span>MySQL</span>
                </div>
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fab fa-git-alt text-gray-600 text-xs"></i>
                    </div>
                    <span>Git</span>
                </div>
            </div>
        </div>

        <!-- Print Button -->
        <div class="bg-gray-50 px-8 py-6 text-center no-print">
            <button onclick="window.print()" 
                class="inline-block bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 transition">
                <i class="fas fa-print mr-2"></i> Print / Save as PDF
            </button>
            <p class="mt-3 text-sm text-gray-600">Use your browser's print function to save as PDF</p>
        </div>
    </div>
</body>
</html>
