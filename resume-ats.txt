BAGUS ALMAHENZAR
Web Developer | Full Stack Developer

Email: <EMAIL>
Phone: +62-XXX-XXXX-XXXX
Location: Pontianak, West Kalimantan, Indonesia
LinkedIn: linkedin.com/in/bagusalmahenzar
GitHub: github.com/bagusalmahenzar

PROFESSIONAL SUMMARY
Experienced Web Developer with 5+ years of expertise in Laravel, React, and Flutter development. Proven track record in developing scalable web applications, CRM systems, and mobile applications. Currently serving as Pranata Komputer Ahli Pertama at BPS-Statistics Pontianak Municipality, specializing in electronic-based government systems (SPBE) and full-stack development.

TECHNICAL SKILLS
Programming Languages: PHP, JavaScript, Dart, HTML5, CSS3
Frameworks: Laravel, React, Node.js, Express.js, Flutter, Tailwind CSS, Bootstrap
Databases: MySQL, PostgreSQL, SQLite, MongoDB
Tools & Technologies: Git, GitHub, Docker, Linux, REST API, Filament, WordPress
Specializations: Full Stack Development, Mobile App Development, CRM Systems, Government Systems (SPBE)

PROFESSIONAL EXPERIENCE

Pranata Komputer Ahli Pertama
BPS-Statistics Pontianak Municipality
2023 - Present
- Led development of Laravel-based Customer Relationship Management (CRM) system with Filament admin panel, improving client management efficiency by 40%
- Built responsive React frontend for customer portal with Express.js backend API, serving 500+ active users
- Responsible for electronic-based government system (SPBE) implementation and maintenance
- Developed automated reporting systems reducing manual data processing time by 60%
- Collaborated with cross-functional teams to deliver government digital transformation projects
- Implemented security best practices and data protection protocols for sensitive government data

Web Developer
Digital Creations
2018 - 2020
- Developed and maintained 15+ Laravel applications for various clients across different industries
- Created custom WordPress themes and plugins, increasing client website performance by 35%
- Implemented responsive designs using Tailwind CSS and Bootstrap frameworks
- Collaborated with design teams to convert PSD/Figma designs into functional web applications
- Optimized database queries and application performance, reducing page load times by 50%
- Provided technical support and maintenance for client websites and applications

EDUCATION

Bachelor of Science in Statistical Computing
Polytechnic of Statistics STIS
2019 - 2023
- Relevant Coursework: Database Management, Web Development, Statistical Programming, Data Analysis
- Capstone Project: Developed a statistical analysis web application using Laravel and R integration

Senior High School Diploma
SMAN 02 Singkawang
2016 - 2019

KEY PROJECTS

Ngambiskuy Educational Platform
- Developed comprehensive educational platform using Laravel, Filament, and JavaScript
- Implemented interactive exam simulation features and learning management system
- Integrated payment gateway and user authentication systems
- Technologies: Laravel, Filament, JavaScript, Tailwind CSS, MySQL

Statistics Indonesia Datalab (StatInaLab)
- Initial system developer for secure microdata access platform
- Designed business logic and system architecture using 5 Safes framework
- Developed virtual desktop integration for secure data access
- Technologies: Laravel, Flutter, Tailwind CSS, MySQL

Indonesian Physiotherapy Association Mobile App
- Developed cross-platform mobile application using Flutter
- Implemented offline data synchronization and member management features
- Integrated with REST APIs for real-time data updates
- Technologies: Flutter, Dart, SQLite, REST API

SEPAKAT Meeting Management System
- Developed CRM system for BPS Statistics Pontianak Municipality
- Implemented lead tracking, sales pipeline, and reporting features
- Technologies: Laravel, Filament, MySQL

CERTIFICATIONS & ADDITIONAL SKILLS
- Laravel Certified Developer
- React Developer Certification
- Flutter Development Certification
- Agile/Scrum Methodology
- Version Control (Git/GitHub)
- Database Design and Optimization
- API Development and Integration
- Mobile App Development
- Government System Development (SPBE)

LANGUAGES
- Indonesian (Native)
- English (Professional Working Proficiency)

ACHIEVEMENTS
- Improved client management efficiency by 40% through CRM system development
- Reduced manual data processing time by 60% with automated reporting systems
- Increased website performance by 35% through optimization techniques
- Successfully served 500+ active users through developed applications
- Led digital transformation projects for government sector
