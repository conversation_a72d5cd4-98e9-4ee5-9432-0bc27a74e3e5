<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>'s Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- EmailJS Library -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    <style>
        /* Custom CSS for animations and effects */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.8s ease-out forwards;
        }

        .delay-100 {
            animation-delay: 0.1s;
        }

        .delay-200 {
            animation-delay: 0.2s;
        }

        .delay-300 {
            animation-delay: 0.3s;
        }

        .delay-400 {
            animation-delay: 0.4s;
        }

        .project-card {
            transition: all 0.3s ease;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .tech-tag {
            transition: all 0.2s ease;
        }

        .tech-tag:hover {
            transform: scale(1.05);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>

<body class="bg-gray-50 font-sans text-gray-800">
    <!-- Navigation -->
    <nav class="fixed w-full bg-white shadow-sm z-50">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <span class="text-xl font-bold text-indigo-600">PORTFOLIO</span>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-indigo-600 hover:text-indigo-800 transition">Home</a>
                    <a href="#projects" class="text-gray-600 hover:text-indigo-600 transition">Projects</a>
                    <a href="#about" class="text-gray-600 hover:text-indigo-600 transition">About</a>
                    <a href="#contact" class="text-gray-600 hover:text-indigo-600 transition">Contact</a>
                    <a href="#resume"
                        class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition">Resume</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="menu-toggle" class="text-gray-600 hover:text-indigo-600">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="#home" class="block px-3 py-2 rounded-md text-base font-medium text-indigo-600">Home</a>
                <a href="#projects"
                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-indigo-600">Projects</a>
                <a href="#about"
                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-indigo-600">About</a>
                <a href="#contact"
                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-indigo-600">Contact</a>
                <a href="#resume"
                    class="block px-3 py-2 rounded-md text-base font-medium bg-indigo-600 text-white">Resume</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="pt-24 pb-16 md:pt-32 md:pb-24 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
        <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 mb-10 md:mb-0 animate-fade-in">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                    Hi, I'm <span class="text-indigo-600">Bagus</span>
                </h1>
                <h2 class="text-2xl md:text-3xl font-semibold text-gray-700 mb-6">
                    Web Developer
                </h2>
                <p class="text-lg text-gray-600 mb-8 max-w-lg">
                    Specializing in Laravel & Filament development. Experienced with React, Express.js, and building
                    cross-platform desktop applications with Flutter.
                </p>
                <div class="flex space-x-4">
                    <a href="#projects"
                        class="bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 transition">
                        View My Work
                    </a>
                    <a href="#contact"
                        class="border border-indigo-600 text-indigo-600 px-6 py-3 rounded-md hover:bg-indigo-50 transition">
                        Contact Me
                    </a>
                </div>
            </div>
            <div class="md:w-1/2 flex justify-center animate-fade-in delay-200">
                <div class="relative">
                    <div
                        class="w-64 h-64 md:w-80 md:h-80 rounded-full overflow-hidden border-4 border-indigo-100 shadow-lg">
                        <img src="/img/photo-bagus-almahenzar.JPG" alt="Profile Photo of Bagus Almahenzar"
                            class="w-full h-full object-cover">
                    </div>
                    <div class="absolute -bottom-4 -right-4 bg-white px-4 py-2 rounded-full shadow-md">
                        <span class="text-indigo-600 font-medium">5+ Years Experience</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-16 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto bg-white">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">My Projects</h2>
            <div class="w-20 h-1 bg-indigo-600 mx-auto"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Projects will be dynamically loaded from data.json -->
            <div id="projects-loading" class="col-span-3 text-center py-12">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600">
                </div>
                <p class="mt-2 text-gray-600">Loading projects...</p>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-16 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto bg-gray-50">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">About Me</h2>
            <div class="w-20 h-1 bg-indigo-600 mx-auto"></div>
        </div>

        <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/3 mb-8 md:mb-0 flex justify-center">
                <div class="w-64 h-64 rounded-full overflow-hidden border-4 border-white shadow-lg">
                    <img src="/img/photo-bagus-almahenzar.JPG" alt="Profile Photo of Bagus Almahenzar"
                        class="w-full h-full object-cover">
                </div>
            </div>

            <div class="md:w-2/3 md:pl-12">
                <h3 class="text-2xl font-semibold mb-4">Who I Am</h3>
                <p class="text-gray-600 mb-6">
                    I'm a passionate web developer with over 5 years of experience building modern, responsive web
                    applications.
                    I specialize in Laravel and Filament development, creating robust backend systems with elegant admin
                    interfaces.
                    My journey in web development has also led me to work with React, Express.js, and Flutter for
                    desktop applications.
                </p>

                <h3 class="text-2xl font-semibold mb-4">My Tech Stack</h3>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fab fa-laravel text-indigo-600"></i>
                        </div>
                        <span>Laravel</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fab fa-react text-blue-600"></i>
                        </div>
                        <span>React</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fab fa-node-js text-green-600"></i>
                        </div>
                        <span>Express.js</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-mobile-alt text-teal-600"></i>
                        </div>
                        <span>Flutter</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fab fa-js text-yellow-600"></i>
                        </div>
                        <span>JavaScript</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fab fa-css3-alt text-blue-600"></i>
                        </div>
                        <span>Tailwind CSS</span>
                    </div>
                </div>

                <div class="flex space-x-4">
                    <a href="#contact"
                        class="bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 transition">
                        Contact Me
                    </a>
                    <!-- <a href="#resume"
                        class="border border-indigo-600 text-indigo-600 px-6 py-3 rounded-md hover:bg-indigo-50 transition">
                        Download Resume
                    </a> -->
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-16 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto bg-white">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Get In Touch</h2>
            <div class="w-20 h-1 bg-indigo-600 mx-auto"></div>
        </div>

        <div class="flex flex-col md:flex-row">
            <div class="md:w-1/2 mb-10 md:mb-0 md:pr-8">
                <h3 class="text-2xl font-semibold mb-4">Contact Information</h3>
                <p class="text-gray-600 mb-8">
                    Feel free to reach out to me for project inquiries, collaborations, or just to say hello.
                    I'm always open to discussing new opportunities and ideas.
                </p>

                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-envelope text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-700">Email</h4>
                            <a href="mailto:<EMAIL>"
                                class="text-indigo-600 hover:underline"><EMAIL></a>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-phone-alt text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-700">Phone</h4>
                            <a href="tel:+6289691077371" class="text-indigo-600 hover:underline">+62 896 9107 7371</a>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-map-marker-alt text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-700">Location</h4>
                            <p class="text-gray-600">Indonesia</p>
                        </div>
                    </div>
                </div>

                <div class="mt-8">
                    <h4 class="font-medium text-gray-700 mb-4">Follow Me</h4>
                    <div class="flex space-x-4">
                        <a href="https://github.com/Dipterocarpaceae"
                            class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-700 hover:bg-indigo-100 hover:text-indigo-600 transition">
                            <i class="fab fa-github"></i>
                        </a>
                        <!-- <a href="#"
                            class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-700 hover:bg-blue-100 hover:text-blue-600 transition">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#"
                            class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-700 hover:bg-blue-100 hover:text-blue-400 transition">
                            <i class="fab fa-twitter"></i>
                        </a> -->
                        <a href="https://www.instagram.com/bagusalmhnzr/"
                            class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-700 hover:bg-pink-100 hover:text-pink-600 transition">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="md:w-1/2">
                <form id="contact-form" class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                        <input type="text" id="name" name="name" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" id="email" name="email" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
                    </div>

                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                        <input type="text" id="subject" name="subject" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                        <textarea id="message" name="message" rows="5" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"></textarea>
                    </div>

                    <button type="submit"
                        class="w-full bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 transition">
                        Send Message
                    </button>

                    <p class="text-sm text-center text-gray-500">
                        Messages will be sent to: <span class="text-indigo-600"><EMAIL></span>
                    </p>
                </form>
                <div id="form-message" class="mt-4 text-center"></div>
            </div>
        </div>
    </section>

    <!-- Resume Section -->
    <section id="resume" class="py-16 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto bg-gray-50">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">My Resume</h2>
            <div class="w-20 h-1 bg-indigo-600 mx-auto"></div>
        </div>

        <div class="bg-white rounded-lg shadow-lg overflow-hidden max-w-3xl mx-auto">
            <div class="p-8">
                <div class="flex flex-col md:flex-row items-center mb-8">
                    <div class="md:w-1/3 mb-6 md:mb-0">
                        <div class="w-32 h-32 rounded-full overflow-hidden border-4 border-indigo-100 mx-auto">
                            <img src="/img/photo-bagus-almahenzar.JPG" alt="Profile Photo of Bagus Almahenzar"
                                class="w-full h-full object-cover">
                        </div>
                    </div>
                    <div class="md:w-2/3 text-center md:text-left">
                        <h1 class="text-3xl font-bold text-gray-900">Bagus Almahenzar</h1>
                        <h2 class="text-xl text-indigo-600 mb-2">Full Stack Web Developer | Software Engineer</h2>
                        <p class="text-gray-600">Specializing in Laravel, React, Flutter, and JavaScript development
                            with 5+ years of experience in web applications, mobile apps, and government systems (SPBE)
                        </p>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-8 mb-8">
                    <h3 class="text-xl font-semibold mb-6 text-indigo-600">Experience</h3>

                    <div class="space-y-6">
                        <div>
                            <h4 class="font-bold text-gray-800">Pranata Komputer Ahli Pertama</h4>
                            <p class="text-gray-600">BPS-Statistics Pontianak Municipality. • 2023 - Present</p>
                            <ul class="list-disc pl-5 mt-2 text-gray-600 space-y-1">
                                <li>Person in charge of electronic based government system (SPBE)</li>
                                <li>State-owned goods operator</li>
                                <li>Member of public relation team</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-bold text-gray-800">Web Developer</h4>
                            <p class="text-gray-600">Statistical Computing • 2020 - Present</p>
                            <ul class="list-disc pl-5 mt-2 text-gray-600 space-y-1">
                                <li>Developed and maintained Laravel applications for various clients</li>
                                <li>Created custom WordPress themes and plugins</li>
                                <li>Implemented responsive designs with Tailwind CSS</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-8 mb-8">
                    <h3 class="text-xl font-semibold mb-6 text-indigo-600">Education</h3>

                    <div class="mt-2">
                        <h4 class="font-bold text-gray-800">B.Sc. in Statistical Computing</h4>
                        <p class="text-gray-600">Polytechnic of Statistics STIS • 2019 - 2023</p>
                    </div>
                    <div class="mt-2">
                        <h4 class="font-bold text-gray-800">Senior High School</h4>
                        <p class="text-gray-600">SMAN 02 Singkawang • 2016 - 2019</p>
                    </div>
                    <div class="mt-2">
                        <h4 class="font-bold text-gray-800">Junior High School</h4>
                        <p class="text-gray-600">SMPN 07 Singkawang • 2013 - 2016</p>
                    </div>
                    <div class="mt-2">
                        <h4 class="font-bold text-gray-800">Primary School</h4>
                        <p class="text-gray-600">SDN 07 Singkawang • 2007 - 2013</p>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-8">
                    <h3 class="text-xl font-semibold mb-6 text-indigo-600">Skills</h3>

                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fab fa-laravel text-indigo-600 text-xs"></i>
                            </div>
                            <span>Laravel</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fab fa-react text-blue-600 text-xs"></i>
                            </div>
                            <span>React</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fab fa-node-js text-green-600 text-xs"></i>
                            </div>
                            <span>Express.js</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-teal-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-mobile-alt text-teal-600 text-xs"></i>
                            </div>
                            <span>Flutter</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fab fa-js text-yellow-600 text-xs"></i>
                            </div>
                            <span>JavaScript</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fab fa-css3-alt text-blue-600 text-xs"></i>
                            </div>
                            <span>Tailwind CSS</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 px-8 py-6 text-center">
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="resume-ats.html" target="_blank"
                        class="inline-block bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 transition">
                        <i class="fas fa-eye mr-2"></i> View ATS Resume
                    </a>
                    <a href="resume-ats.txt" download="Bagus_Almahenzar_Resume.txt"
                        class="inline-block bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition">
                        <i class="fas fa-download mr-2"></i> Download ATS Resume
                    </a>
                </div>
                <p class="mt-3 text-sm text-gray-600">ATS-friendly format optimized for applicant tracking systems</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <span class="text-xl font-bold text-indigo-400">PORTFOLIO</span>
                    <p class="text-gray-400 mt-2">Building digital experiences that matter</p>
                </div>

                <div class="flex space-x-6">
                    <a href="https://github.com/Dipterocarpaceae" class="text-gray-400 hover:text-white transition">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                    <!-- <a href="#" class="text-gray-400 hover:text-blue-400 transition">
                        <i class="fab fa-linkedin-in text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-500 transition">
                        <i class="fab fa-twitter text-xl"></i>
                    </a> -->
                    <a href="https://www.instagram.com/bagusalmhnzr/"
                        class="text-gray-400 hover:text-pink-500 transition">
                        <i class="fab fa-instagram text-xl"></i>
                    </a>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 mb-4 md:mb-0">© 2025 Bagus Almahenzar's Portfolio. All rights reserved.</p>

                <div class="flex space-x-6">
                    <a href="#home" class="text-gray-400 hover:text-white transition">Home</a>
                    <a href="#projects" class="text-gray-400 hover:text-white transition">Projects</a>
                    <a href="#about" class="text-gray-400 hover:text-white transition">About</a>
                    <a href="#contact" class="text-gray-400 hover:text-white transition">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Load scripts -->
    <script src="js/projects.js"></script>
    <script src="js/contact.js"></script>

    <script>
        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', function () {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    // Close mobile menu if open
                    const menu = document.getElementById('mobile-menu');
                    menu.classList.add('hidden');

                    // Scroll to target
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add animation class to elements when they come into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, {
            threshold: 0.1
        });

        // Initial observation of elements
        document.querySelectorAll('.project-card, .animate-fade-in').forEach(el => {
            observer.observe(el);
        });

        // Set up a mutation observer to watch for dynamically added project cards
        const projectsContainer = document.querySelector('#projects .grid');
        if (projectsContainer) {
            const mutationObserver = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === 1 && node.classList.contains('project-card')) {
                                observer.observe(node);
                            }
                        });
                    }
                });
            });

            mutationObserver.observe(projectsContainer, { childList: true });
        }
    </script>
</body>

</html>