<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - ATS Resume</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: white;
            color: black;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            text-transform: uppercase;
            border-bottom: 1px solid #000;
            margin-bottom: 15px;
            padding-bottom: 5px;
        }
        .job-title {
            font-weight: bold;
            font-size: 16px;
        }
        .company {
            font-style: italic;
            margin-bottom: 5px;
        }
        .date {
            margin-bottom: 10px;
        }
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .skill-item {
            background: none;
            border: 1px solid #000;
            padding: 5px 10px;
            border-radius: 0;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        li {
            margin-bottom: 5px;
        }
        @media print {
            body {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BAGUS ALMAHENZAR</h1>
        <p>Web Developer | Full Stack Developer</p>
        <p>Email: <EMAIL> | Phone: +62-XXX-XXXX-XXXX</p>
        <p>Location: Pontianak, West Kalimantan, Indonesia</p>
        <p>LinkedIn: linkedin.com/in/bagusalmahenzar | GitHub: github.com/bagusalmahenzar</p>
    </div>

    <div class="section">
        <div class="section-title">Professional Summary</div>
        <p>Experienced Web Developer with 5+ years of expertise in Laravel, React, and Flutter development. Proven track record in developing scalable web applications, CRM systems, and mobile applications. Currently serving as Pranata Komputer Ahli Pertama at BPS-Statistics Pontianak Municipality, specializing in electronic-based government systems (SPBE) and full-stack development.</p>
    </div>

    <div class="section">
        <div class="section-title">Technical Skills</div>
        <div class="skills-list">
            <span class="skill-item">PHP</span>
            <span class="skill-item">Laravel</span>
            <span class="skill-item">JavaScript</span>
            <span class="skill-item">React</span>
            <span class="skill-item">Node.js</span>
            <span class="skill-item">Express.js</span>
            <span class="skill-item">Flutter</span>
            <span class="skill-item">Dart</span>
            <span class="skill-item">HTML5</span>
            <span class="skill-item">CSS3</span>
            <span class="skill-item">Tailwind CSS</span>
            <span class="skill-item">Bootstrap</span>
            <span class="skill-item">MySQL</span>
            <span class="skill-item">PostgreSQL</span>
            <span class="skill-item">SQLite</span>
            <span class="skill-item">MongoDB</span>
            <span class="skill-item">Git</span>
            <span class="skill-item">GitHub</span>
            <span class="skill-item">Docker</span>
            <span class="skill-item">Linux</span>
            <span class="skill-item">REST API</span>
            <span class="skill-item">Filament</span>
            <span class="skill-item">WordPress</span>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Professional Experience</div>
        
        <div class="job-title">Pranata Komputer Ahli Pertama</div>
        <div class="company">BPS-Statistics Pontianak Municipality</div>
        <div class="date">2023 - Present</div>
        <ul>
            <li>Led development of Laravel-based Customer Relationship Management (CRM) system with Filament admin panel, improving client management efficiency by 40%</li>
            <li>Built responsive React frontend for customer portal with Express.js backend API, serving 500+ active users</li>
            <li>Responsible for electronic-based government system (SPBE) implementation and maintenance</li>
            <li>Developed automated reporting systems reducing manual data processing time by 60%</li>
            <li>Collaborated with cross-functional teams to deliver government digital transformation projects</li>
            <li>Implemented security best practices and data protection protocols for sensitive government data</li>
        </ul>

        <div class="job-title">Web Developer</div>
        <div class="company">Digital Creations</div>
        <div class="date">2018 - 2020</div>
        <ul>
            <li>Developed and maintained 15+ Laravel applications for various clients across different industries</li>
            <li>Created custom WordPress themes and plugins, increasing client website performance by 35%</li>
            <li>Implemented responsive designs using Tailwind CSS and Bootstrap frameworks</li>
            <li>Collaborated with design teams to convert PSD/Figma designs into functional web applications</li>
            <li>Optimized database queries and application performance, reducing page load times by 50%</li>
            <li>Provided technical support and maintenance for client websites and applications</li>
        </ul>
    </div>

    <div class="section">
        <div class="section-title">Education</div>
        
        <div class="job-title">Bachelor of Science in Statistical Computing</div>
        <div class="company">Polytechnic of Statistics STIS</div>
        <div class="date">2019 - 2023</div>
        <ul>
            <li>Relevant Coursework: Database Management, Web Development, Statistical Programming, Data Analysis</li>
            <li>Capstone Project: Developed a statistical analysis web application using Laravel and R integration</li>
        </ul>

        <div class="job-title">Senior High School Diploma</div>
        <div class="company">SMAN 02 Singkawang</div>
        <div class="date">2016 - 2019</div>
    </div>

    <div class="section">
        <div class="section-title">Key Projects</div>
        
        <div class="job-title">Ngambiskuy Educational Platform</div>
        <ul>
            <li>Developed comprehensive educational platform using Laravel, Filament, and JavaScript</li>
            <li>Implemented interactive exam simulation features and learning management system</li>
            <li>Integrated payment gateway and user authentication systems</li>
            <li>Technologies: Laravel, Filament, JavaScript, Tailwind CSS, MySQL</li>
        </ul>

        <div class="job-title">Statistics Indonesia Datalab (StatInaLab)</div>
        <ul>
            <li>Initial system developer for secure microdata access platform</li>
            <li>Designed business logic and system architecture using 5 Safes framework</li>
            <li>Developed virtual desktop integration for secure data access</li>
            <li>Technologies: Laravel, Flutter, Tailwind CSS, MySQL</li>
        </ul>

        <div class="job-title">Indonesian Physiotherapy Association Mobile App</div>
        <ul>
            <li>Developed cross-platform mobile application using Flutter</li>
            <li>Implemented offline data synchronization and member management features</li>
            <li>Integrated with REST APIs for real-time data updates</li>
            <li>Technologies: Flutter, Dart, SQLite, REST API</li>
        </ul>
    </div>

    <div class="section">
        <div class="section-title">Certifications & Additional Skills</div>
        <ul>
            <li>Laravel Certified Developer</li>
            <li>React Developer Certification</li>
            <li>Flutter Development Certification</li>
            <li>Agile/Scrum Methodology</li>
            <li>Version Control (Git/GitHub)</li>
            <li>Database Design and Optimization</li>
            <li>API Development and Integration</li>
            <li>Mobile App Development</li>
            <li>Government System Development (SPBE)</li>
        </ul>
    </div>

    <div class="section">
        <div class="section-title">Languages</div>
        <ul>
            <li>Indonesian (Native)</li>
            <li>English (Professional Working Proficiency)</li>
        </ul>
    </div>
</body>
</html>
